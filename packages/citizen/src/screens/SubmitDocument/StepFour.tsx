import React, {memo, useEffect, useState, useCallback} from 'react';
import {StyleSheet, View, ScrollView, BackHandler} from 'react-native';
import {
  Button,
  useTheme,
  Text,
  Card,
  Divider,
  HelperText,
  Menu,
  IconButton,
} from 'react-native-paper';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {IOptionMenuItem, StepMenuVertical} from '../../components/AcStepMenu';
import {
  useNavigation,
  NavigationProp,
  useFocusEffect,
} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useCommonProcedure} from '../../stores';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {dvcQuangNinh} from '../../requester-biz-service/apis/dvc-quang-ninh';
import {useMutation} from 'react-query';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: '<PERSON><PERSON> b<PERSON><PERSON> thông tin người yêu cầu'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

const PaymentRow = ({
  label,
  value,
  style,
}: {
  label: string;
  value: string | number;
  style?: any;
}) => (
  <View style={styles.paymentRow}>
    <Text style={style}>{label}</Text>
    <Text style={style}>{value}</Text>
  </View>
);

export const StepFour = memo(() => {
  const theme = useTheme();
  const [title] = React.useState<string>('Nhận kết quả & thanh toán');
  const forwardData = useCommonProcedure(state => state.forwardData);
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const templateDetail = useCommonProcedure(state => state.templateDetail);
  const submitResponse = useSubmitFlow(
    state => state.submitDocumentInstantResponse,
  );
  const currentUuid = useSubmitFlow(state => state.currentUuid);
  const paymentInfo = useSubmitFlow(state => state.paymentInfo);
  const setPaymentInfo = useSubmitFlow(state => state.setPaymentInfo);

  // React Query mutation for getPaymentInfoDetail
  const {mutate: fetchPaymentInfo, isLoading: isPaymentLoading} = useMutation(
    async () => {
      const hoSoKemTheo = templateDetail.partnerData.hoSoKemTheo.find(
        (item: {eform: boolean}) => item.eform === true,
      );
      const hoSoKemTheoId = hoSoKemTheo?.hoSoKemTheoID;
      console.log(submitResponse, '<submitResponse>');
      const res = await dvcQuangNinh.getPaymentInfoDetail({
        hoSoId: `${submitResponse?.data?.hoSoID}`,
        keyEformId: currentUuid || '',
        donViId: `${submitResponse?.data?.donViID}`,
        linhVucId: `${submitResponse?.data?.linhVucID}`,
        hoSoKemTheoId,
        thuTucHanhChinhId: `${submitResponse?.data?.thuTucHanhChinhID}`,
      });
      return res.data;
    },
    {
      onSuccess: data => {
        console.log('Payment info fetched successfully:', data.data);
        setPaymentInfo(data.data);
      },
      onError: error => {
        console.error('Error fetching payment info:', JSON.stringify(error));
        setPaymentInfo(null);
      },
    },
  );

  useEffect(() => {
    fetchPaymentInfo();
  }, [fetchPaymentInfo]);

  // Handle back button press
  const handleBackPress = useCallback(() => {
    nav.goBack();
  }, [nav]);

  // Handle hardware back button (Android)
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  // Fake data for dropdown and payment info
  const [selectedLocation, setSelectedLocation] = useState(
    forwardData?.TenDonViThucHien || '',
  );
  const [locationMenuVisible, setLocationMenuVisible] = useState(false);
  const locations = [forwardData?.TenDonViThucHien || ''];
  // Fake payment info (now includes all fields from Flutter reference)
  const colorStyles = {
    sectionTitle: {color: theme.colors.onSurface},
    infoBox: {backgroundColor: theme.colors.surfaceVariant},
    infoBoxText: {color: theme.colors.primary},
    subLabel: {color: theme.colors.onSurfaceVariant},
    dropdown: {
      borderColor: theme.colors.outline || theme.colors.primaryContainer,
    },
    helperText: {color: theme.colors.onSurfaceVariant},
    divider: {
      backgroundColor: theme.colors.outline || theme.colors.primaryContainer,
    },
    totalFee: {color: theme.colors.onSurface},
    footer: {
      backgroundColor: theme.colors.background,
      borderColor: theme.colors.outline || theme.colors.primaryContainer,
    },
    outlinedBtn: {backgroundColor: theme.colors.surfaceVariant},
    outlinedLabel: {color: theme.colors.onSurface},
    containedBtn: {backgroundColor: theme.colors.error},
    containedLabel: {color: theme.colors.onPrimary},
  };

  if (isPaymentLoading && !paymentInfo) {
    return (
      <View
        style={[
          styles.container,
          colorStyles.footer,
          {justifyContent: 'center', alignItems: 'center'},
        ]}>
        <Text style={[styles.sectionTitle, colorStyles.sectionTitle]}>
          Đang tải thông tin thanh toán...
        </Text>
        <View style={{marginTop: 24}}>
          <HelperText type="info">Vui lòng chờ</HelperText>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, colorStyles.footer]}>
      <CCAppBar label={title} isBack={true} />
      <View style={styles.stepMenuVertical}>
        <StepMenuVertical currentStep={4} options={optionSteps} />
      </View>
      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        {/* Phương thức giao nhận hồ sơ */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, colorStyles.sectionTitle]}>
              Phương thức giao nhận hồ sơ
            </Text>
            <View style={[styles.infoBox, colorStyles.infoBox]}>
              <Text style={[styles.infoBoxText, colorStyles.infoBoxText]}>
                Kết quả giải quyết thủ tục hành chính của bạn sẽ được hệ thống
                gửi trực tiếp vào "Giấy tờ của tôi" dưới dạng tệp điện tử
              </Text>
            </View>
            <View style={styles.deliveryRowWrap}>
              <View style={styles.deliveryRow}>
                <IconButton
                  icon="file-document-outline"
                  size={24}
                  // style={styles.iconCircle}
                />
                <Text style={[styles.subLabel, colorStyles.subLabel]}>
                  Nhận kết quả bản giấy đã ký đóng dấu, tại:
                </Text>
              </View>
              <Menu
                visible={locationMenuVisible}
                onDismiss={() => setLocationMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    style={[styles.dropdown, colorStyles.dropdown]}
                    onPress={() => setLocationMenuVisible(true)}
                    // contentStyle={styles.dropdownContent}
                  >
                    {selectedLocation}
                  </Button>
                }>
                {locations.map(loc => (
                  <Menu.Item
                    key={loc}
                    onPress={() => {
                      setSelectedLocation(loc);
                      setLocationMenuVisible(false);
                    }}
                    title={loc}
                  />
                ))}
              </Menu>
            </View>
          </Card.Content>
        </Card>

        {/* Thông tin thanh toán */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, colorStyles.sectionTitle]}>
              Thông tin thanh toán
            </Text>
            <View style={[styles.infoBox, colorStyles.infoBox]}>
              <Text style={[styles.infoBoxText, colorStyles.infoBoxText]}>
                Thanh toán {paymentInfo?.nopHoSoSoTienCanThanhToanSau} VNĐ sau
                khi nhận kết quả hồ sơ!
              </Text>
            </View>
            {paymentInfo?.phiNopTruoc > 0 && (
              <PaymentRow
                label="Phí nộp trước:"
                value={`${paymentInfo?.phiNopTruoc} VNĐ`}
                style={[styles.subLabel, colorStyles.subLabel]}
              />
            )}
            {paymentInfo?.phiNopSau > 0 && (
              <PaymentRow
                label="Phí nộp sau:"
                value={`${paymentInfo?.phiNopSau} VNĐ`}
                style={[styles.subLabel, colorStyles.subLabel]}
              />
            )}
            {paymentInfo?.lePhiNopTruoc > 0 && (
              <PaymentRow
                label="Lệ phí nộp trước:"
                value={`${paymentInfo?.lePhiNopTruoc} VNĐ`}
                style={[styles.subLabel, colorStyles.subLabel]}
              />
            )}
            {paymentInfo?.lePhiNopSau > 0 && (
              <PaymentRow
                label="Lệ phí nộp sau:"
                value={`${paymentInfo?.lePhiNopSau} VNĐ`}
                style={[styles.subLabel, colorStyles.subLabel]}
              />
            )}
            <View style={styles.paymentRow}>
              <IconButton
                icon="file-document-outline"
                size={24}
                // style={styles.iconCircle}
              />
              <Text style={[styles.subLabel, colorStyles.subLabel]}>
                Số lượng bản sao: {paymentInfo?.soLuongBanSao} bản
              </Text>
            </View>
            <HelperText
              type="info"
              style={[styles.helperText, colorStyles.helperText]}>
              (Chi phí cho số lượng bản sao trên là {paymentInfo?.soTienBanSao}{' '}
              VNĐ)
            </HelperText>
            <Divider style={[styles.divider, colorStyles.divider]} />
            <View style={styles.feeRow}>
              <Text style={[styles.sectionTitle, colorStyles.sectionTitle]}>
                Tổng phí
              </Text>
              <Text style={[styles.totalFee, colorStyles.totalFee]}>
                {`${paymentInfo?.tongTien}`} VNĐ
              </Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
      <View style={[styles.footer, colorStyles.footer]}>
        <View style={styles.footerRow}>
          {/* <Button
            mode="outlined"
            onPress={() => nav.goBack()}
            style={[styles.flex1]}
            labelStyle={colorStyles.outlinedLabel}>
            Quay lại
          </Button> */}
          <Button
            mode="contained"
            style={[styles.flex1]}
            onPress={() => {
              // TODO: Add next step logic here
              if (paymentInfo.nopHoSoSoTienThanhToan === 0) {
                nav.navigate('SuccessStep', {
                  soBienNhan: submitResponse?.data?.soBienNhan,
                  nopHoSoSoTienThanhToan: paymentInfo.nopHoSoSoTienThanhToan,
                  nopHoSoSoTienCanThanhToanSau:
                    paymentInfo.nopHoSoSoTienCanThanhToanSau,
                  tongTien: paymentInfo.tongTien,
                  title: templateDetail?.partnerData?.tenThuTucHanhChinh,
                  isPayment: paymentInfo.nopHoSoSoTienCanThanhToanSau > 0,
                });
              }
            }}
            labelStyle={colorStyles.containedLabel}>
            Tiếp theo
          </Button>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 120,
    paddingHorizontal: 0,
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  card: {
    margin: 8,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  infoBox: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoBoxText: {
    fontSize: 14,
    flex: 1,
  },
  subLabel: {
    fontSize: 14,
    marginLeft: 4,
    flex: 1,
  },
  paymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 0,
    gap: 8,
  },
  deliveryRowWrap: {
    marginTop: 16,
    marginBottom: 8,
  },
  deliveryRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    marginVertical: 12,
  },
  dropdown: {
    marginTop: 8,
    borderRadius: 8,
    borderWidth: 1,
    width: '100%',
    justifyContent: 'flex-start',
  },
  helperText: {
    marginLeft: 36,
    color: '#637381',
    fontSize: 12,
    marginBottom: 0,
  },
  feeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  totalFee: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  footer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 8,
    zIndex: 10,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  flex1: {
    flex: 1,
  },
});

export default StepFour;
